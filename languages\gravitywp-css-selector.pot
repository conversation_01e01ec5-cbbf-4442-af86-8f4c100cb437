#, fuzzy
msgid ""
msgstr ""
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"Project-Id-Version: GravityWP - CSS Selector\n"
"POT-Creation-Date: 2025-04-16 20:05+0200\n"
"PO-Revision-Date: 2025-04-16 20:04+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4.3\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: gravitywp-css-selector.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"

#: gravitywp-css-selector.php:35
msgid "Select a CSS ready class"
msgstr ""

#: gravitywp-css-selector.php:43
msgid "Two Columns (2)"
msgstr ""

#: gravitywp-css-selector.php:45
msgid "Left Half"
msgstr ""

#: gravitywp-css-selector.php:46
msgid "Right Half"
msgstr ""

#: gravitywp-css-selector.php:50
msgid "Three Columns (3)"
msgstr ""

#: gravitywp-css-selector.php:52
msgid "Left Third"
msgstr ""

#: gravitywp-css-selector.php:53
msgid "Middle Third"
msgstr ""

#: gravitywp-css-selector.php:54
msgid "Right Third"
msgstr ""

#: gravitywp-css-selector.php:58
msgid "Four Columns (4)"
msgstr ""

#: gravitywp-css-selector.php:60
msgid "1st Quarter"
msgstr ""

#: gravitywp-css-selector.php:61
msgid "2nd Quarter"
msgstr ""

#: gravitywp-css-selector.php:62
msgid "3rd Quarter"
msgstr ""

#: gravitywp-css-selector.php:63
msgid "4th Quarter"
msgstr ""

#: gravitywp-css-selector.php:71
msgid "Radio Buttons & Checkboxes"
msgstr ""

#: gravitywp-css-selector.php:73
msgid ""
"Turn choices into an inline horizontal list (not evenly spaced columns)."
msgstr ""

#: gravitywp-css-selector.php:73
msgid "List Inline"
msgstr ""

#: gravitywp-css-selector.php:74
msgid ""
"Show choices in two (2) columns, left to right and then descending in rows."
msgstr ""

#: gravitywp-css-selector.php:74 gravitywp-css-selector.php:75
#: gravitywp-css-selector.php:76 gravitywp-css-selector.php:77
msgid "Columns"
msgstr ""

#: gravitywp-css-selector.php:75
msgid ""
"Show choices in three (3) columns, left to right and then descending in rows."
msgstr ""

#: gravitywp-css-selector.php:76
msgid ""
"Show choices in four (4) columns, left to right and then descending in rows."
msgstr ""

#: gravitywp-css-selector.php:77
msgid ""
"Show choices in five (5) columns, left to right and then descending in rows."
msgstr ""

#: gravitywp-css-selector.php:84
msgid ""
"Show choices in two (2) columns, top to bottom and then the next column."
msgstr ""

#: gravitywp-css-selector.php:84 gravitywp-css-selector.php:85
#: gravitywp-css-selector.php:86 gravitywp-css-selector.php:87
msgid "Col Vertical"
msgstr ""

#: gravitywp-css-selector.php:85
msgid ""
"Show choices in three (3) columns, top to bottom and then the next column."
msgstr ""

#: gravitywp-css-selector.php:86
msgid ""
"Show choices in four (4) columns, top to bottom and then the next column."
msgstr ""

#: gravitywp-css-selector.php:87
msgid ""
"Show choices in five (5) columns, top to bottom and then the next column."
msgstr ""

#: gravitywp-css-selector.php:94
msgid "Applies 25px height to all choices."
msgstr ""

#: gravitywp-css-selector.php:94
msgid "Height"
msgstr ""

#: gravitywp-css-selector.php:95
msgid "Applies 50px height to all choices."
msgstr ""

#: gravitywp-css-selector.php:96
msgid "Applies 75px height to all choices."
msgstr ""

#: gravitywp-css-selector.php:97
msgid "Applies 100px height to all choices."
msgstr ""

#: gravitywp-css-selector.php:98
msgid "Applies 125px height to all choices."
msgstr ""

#: gravitywp-css-selector.php:99
msgid "Applies 150px height to all choices."
msgstr ""

#: gravitywp-css-selector.php:107
msgid "HTML Block Classes"
msgstr ""

#: gravitywp-css-selector.php:109
msgid "This turns an HTML field and its contents into a green banner message."
msgstr ""

#: gravitywp-css-selector.php:109
msgid "Green Alert"
msgstr ""

#: gravitywp-css-selector.php:110
msgid "This turns an HTML field and its contents into a red banner message."
msgstr ""

#: gravitywp-css-selector.php:110
msgid "Red Alert"
msgstr ""

#: gravitywp-css-selector.php:111
msgid "This turns an HTML field and its contents into a yellow banner message."
msgstr ""

#: gravitywp-css-selector.php:111
msgid "Yellow Alert"
msgstr ""

#: gravitywp-css-selector.php:112
msgid "This turns an HTML field and its contents into a gray banner message."
msgstr ""

#: gravitywp-css-selector.php:112
msgid "Gray Alert"
msgstr ""

#: gravitywp-css-selector.php:113
msgid "This turns an HTML field and its contents into a blue banner message."
msgstr ""

#: gravitywp-css-selector.php:113
msgid "Blue Alert"
msgstr ""

#: gravitywp-css-selector.php:121
msgid "Others"
msgstr ""

#: gravitywp-css-selector.php:123
msgid ""
"Hides a field, useful for field types where the Visibility setting is not "
"available, like product fields."
msgstr ""

#: gravitywp-css-selector.php:123
msgid "Invisible Field"
msgstr ""

#: gravitywp-css-selector.php:124
msgid ""
"Places the field inline horizontally with other fields but does not create "
"equally-spaced column layouts."
msgstr ""

#: gravitywp-css-selector.php:124
msgid "Inline Field"
msgstr ""

#: gravitywp-css-selector.php:125
msgid ""
"Converts a section break field into a box with a fixed height that will "
"automatically show a scroll bar if there’s a large amount of text."
msgstr ""

#: gravitywp-css-selector.php:125
msgid "Scrolling Paragraph Text"
msgstr ""

#: gravitywp-css-selector.php:128
msgid "Hides the am/pm selector in the time field."
msgstr ""

#: gravitywp-css-selector.php:128
msgid "Hide Time am/pm"
msgstr ""

#: gravitywp-css-selector.php:129
msgid ""
"Hides the characters left counter beneath paragraph text fields when using "
"the maximum characters option."
msgstr ""

#: gravitywp-css-selector.php:129
msgid "Hide Character Counter"
msgstr ""

#: gravitywp-css-selector.php:136
msgid "Gravity PDF"
msgstr ""

#: gravitywp-css-selector.php:138
msgid "Excludes the field from the PDF."
msgstr ""

#: gravitywp-css-selector.php:138
msgid "Exclude from PDF"
msgstr ""

#: gravitywp-css-selector.php:139
msgid "Starts a new PDF page with this field at the top of the new page."
msgstr ""

#: gravitywp-css-selector.php:139
msgid "Pagebreak"
msgstr ""

#: gravitywp-css-selector.php:143
msgid "Gravity Wiz (Perks)"
msgstr ""

#: gravitywp-css-selector.php:145
msgid "Copies the value of Field ID 1 to Field ID 2 (Gravity Perks needed)"
msgstr ""

#: gravitywp-css-selector.php:145
msgid "Copy Cat"
msgstr ""

#: gravitywp-css-selector.php:154
msgid "Help"
msgstr ""

#: gravitywp-css-selector.php:156
msgid "Add custom css"
msgstr ""

#: gravitywp-css-selector.php:157
msgid "Official Gravity Forms Documentation"
msgstr ""

#: gravitywp-css-selector.php:160
msgid "Tip: click twice, add css, close window "
msgstr ""

#. Plugin Name of the plugin/theme
msgid "GravityWP - CSS Selector"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://gravitywp.com/plugins/css-selector/"
msgstr ""

#. Description of the plugin/theme
msgid "Easily select a Gravity Forms CSS Ready Class for your form fields."
msgstr ""

#. Author of the plugin/theme
msgid "GravityWP"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://gravitywp.com"
msgstr ""
